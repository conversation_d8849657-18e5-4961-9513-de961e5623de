{"name": "readandco", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "eslint . && tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop", "db:intro": "drizzle-kit introspect", "dev": "next dev --turbo", "lint": "eslint .", "lint:fix": "eslint --fix .", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "patch-package"}, "dependencies": {"@auth/drizzle-adapter": "^1.7.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.13.0", "@tanstack/react-query": "^5.64.0", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.1.2", "@trpc/server": "^11.0.0", "@trpc/tanstack-react-query": "^11.0.0", "@vis.gl/react-google-maps": "^1.5.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.0", "drizzle-seed": "^0.3.0", "drizzle-zod": "^0.8.1", "exceljs": "^4.4.0", "fuse.js": "^7.1.0", "geist": "^1.3.0", "lucide-react": "^0.541.0", "next": "^15.0.1", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "patch-package": "^8.0.0", "postgres": "^3.4.4", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "recharts": "^2.15.4", "superjson": "^2.2.1", "tailwind-merge": "^3.2.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zod": "^4.1.1"}, "devDependencies": {"@dotenvx/dotenvx": "^1.32.1", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@tailwindcss/postcss": "^4.1.4", "@types/eslint": "^9.6.1", "@types/google.maps": "^3.58.1", "@types/node": "^24.3.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "babel-plugin-react-compiler": "^19.1.0-rc.1", "drizzle-kit": "^0.31.0", "eslint": "^9.18.0", "eslint-config-next": "^15.0.1", "eslint-plugin-drizzle": "^0.2.3", "eslint-plugin-react-compiler": "^19.1.0-rc.1", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.4", "tsx": "^4.19.2", "tw-animate-css": "^1.2.8", "typescript": "^5.5.3"}, "ct3aMetadata": {"initVersion": "7.38.1"}, "packageManager": "npm@10.9.2"}