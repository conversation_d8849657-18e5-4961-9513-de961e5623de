import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { CalendarRangePicker } from "~/components/ui/calendarrangepicker";
import { Button } from "~/components/ui/button";
import { type newStoreRow, publisherToFunction } from "~/lib/ingest/common";
import { findExistingStores, insertRecords } from "~/lib/ingest/insert";
import { type DateRange } from "react-day-picker";
import type { InferSelectModel } from "drizzle-orm";
import type { reportTypes } from "~/server/db/schema";
import type { getAllPublishers, getPublisher } from "~/server/queries";
import type * as XLSX from "xlsx";

interface SheetOptionsProps {
  publishers: getAllPublishers;
  workbook: XLSX.WorkBook | null;
  worksheet: XLSX.WorkSheet | undefined;
  selectedPublisher: getPublisher;
  selectedReportType: InferSelectModel<typeof reportTypes> | undefined;
  dateRange: DateRange | undefined;
  setDateRange: (date: DateRange | undefined) => void;
  setNewStores: (stores: newStoreRow[] | null) => void;
  isPending: boolean;
  error: string | null;
  spreadsheet: File;
  setSelectedPublisher: (publisher: getPublisher) => void;
  setWorksheet: (worksheet: XLSX.WorkSheet | undefined) => void;
  setSelectedReportType: (
    reportType: InferSelectModel<typeof reportTypes> | undefined,
  ) => void;
}

export function SheetOptions(props: SheetOptionsProps) {
  const handleParseSubmit = async () => {
    if (
      !props.workbook ||
      !props.worksheet ||
      !props.selectedReportType ||
      !props.selectedPublisher
    )
      return;

    try {
      const cleanRecords = await publisherToFunction[
        props.selectedPublisher.name
      ]!(props.worksheet);
      const recordsWithIds = await findExistingStores(
        props.selectedPublisher.name,
        cleanRecords,
      );
      console.log("Processing complete", {
        recordsWithIds,
      });
      if (recordsWithIds.existing.length > 0) {
        await insertRecords(recordsWithIds.existing, props.dateRange);
      }
      console.log(`Uploaded ${recordsWithIds.existing.length} records`);
      console.log(recordsWithIds.new);
      props.setNewStores(recordsWithIds.new);
    } catch (err) {
      console.error("Error processing data", err);
    }
  };

  return (
    <>
      <div className="space-y-4">
        <p className="w-96 text-center text-lg">
          {props.spreadsheet.name}
          {props.isPending && " (Processing...)"}
        </p>

        {props.error && <p className="text-sm text-red-500">{props.error}</p>}

        <Select
          onValueChange={(value) => {
            props.setSelectedPublisher(
              props.publishers.find((p) => p.id.toString() === value),
            );
          }}
        >
          <SelectTrigger className="size-96">
            <SelectValue placeholder="Specify the publisher" />
          </SelectTrigger>
          <SelectContent>
            {props.publishers.map((publisher) => (
              <SelectItem key={publisher.id} value={publisher.id.toString()}>
                {publisher.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          onValueChange={(value) => {
            props.setWorksheet(props.workbook?.Sheets[value]);
          }}
        >
          <SelectTrigger className={"size-96"}>
            <SelectValue placeholder="Specify the worksheet" />
          </SelectTrigger>
          <SelectContent>
            {props.workbook?.SheetNames.map((worksheet) => (
              <SelectItem key={worksheet} value={worksheet}>
                {worksheet}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          disabled={!props.selectedPublisher}
          onValueChange={(value) => {
            props.setSelectedReportType(
              props.selectedPublisher?.reportTypes.find(
                (p) => p.id.toString() === value,
              ),
            );
          }}
        >
          <SelectTrigger className={"size-96"}>
            <SelectValue placeholder="Type of report" />
          </SelectTrigger>
          <SelectContent>
            {props.selectedPublisher?.reportTypes.map((reportType) => (
              <SelectItem key={reportType.id} value={reportType.id.toString()}>
                {reportType.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {props.selectedReportType?.dateNeeded && (
          <CalendarRangePicker
            dateRange={props.dateRange}
            setDateRange={props.setDateRange}
          />
        )}

        {props.selectedReportType &&
          ((props.selectedReportType.dateNeeded && props.dateRange?.to) ??
            !props.selectedReportType.dateNeeded) && (
            <Button
              onClick={() => void handleParseSubmit()}
              disabled={props.isPending || !props.workbook}
            >
              {props.isPending ? "Processing..." : "Submit"}
            </Button>
          )}
      </div>
    </>
  );
}
