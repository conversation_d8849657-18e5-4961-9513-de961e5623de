"use client";

import { Progress } from "~/components/ui/progress";
import { CheckCircle, Circle, Loader2 } from "lucide-react";

export interface UploadProgressStep {
  id: string;
  label: string;
  status: "pending" | "in-progress" | "completed" | "error";
  current?: number;
  total?: number;
  error?: string;
}

interface UploadProgressProps {
  steps: UploadProgressStep[];
  currentStep?: string;
  overallProgress?: number;
  className?: string;
}

export function UploadProgress({
  steps,
  currentStep,
  overallProgress,
  className = "",
}: UploadProgressProps) {
  const getStepIcon = (step: UploadProgressStep) => {
    switch (step.status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "in-progress":
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case "error":
        return <Circle className="h-5 w-5 text-red-500" />;
      default:
        return <Circle className="h-5 w-5 text-gray-300" />;
    }
  };

  const getProgressPercentage = (step: UploadProgressStep) => {
    if (step.status === "completed") return 100;
    if (step.status === "pending") return 0;
    if (
      step.current !== undefined &&
      step.total !== undefined &&
      step.total > 0
    ) {
      return Math.round((step.current / step.total) * 100);
    }
    return 0;
  };

  const currentStepData = steps.find((step) => step.id === currentStep);
  const completedSteps = steps.filter(
    (step) => step.status === "completed",
  ).length;
  const totalSteps = steps.length;
  const calculatedOverallProgress =
    overallProgress ?? Math.round((completedSteps / totalSteps) * 100);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Progress */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Upload Progress</h3>
          <span className="text-muted-foreground text-sm">
            {calculatedOverallProgress}% Complete
          </span>
        </div>
        <Progress value={calculatedOverallProgress} className="h-2" />
      </div>

      {/* Current Step Details */}
      {currentStepData && currentStepData.status === "in-progress" && (
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="mb-2 flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            <span className="font-medium text-blue-900">
              {currentStepData.label}
            </span>
          </div>

          {currentStepData.current !== undefined &&
            currentStepData.total !== undefined && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-blue-700">
                  <span>
                    Processing {currentStepData.current} of{" "}
                    {currentStepData.total} records
                  </span>
                  <span>{getProgressPercentage(currentStepData)}%</span>
                </div>
                <Progress
                  value={getProgressPercentage(currentStepData)}
                  className="h-1.5"
                />
              </div>
            )}
        </div>
      )}

      {/* Step List */}
      <div className="space-y-3">
        {steps.map((step) => (
          <div key={step.id} className="flex items-center gap-3">
            {getStepIcon(step)}

            <div className="min-w-0 flex-1">
              <div className="flex items-center justify-between">
                <span
                  className={`font-medium ${
                    step.status === "completed"
                      ? "text-green-700"
                      : step.status === "in-progress"
                        ? "text-blue-700"
                        : step.status === "error"
                          ? "text-red-700"
                          : "text-gray-500"
                  }`}
                >
                  {step.label}
                </span>

                {step.current !== undefined && step.total !== undefined && (
                  <span className="text-muted-foreground text-sm">
                    {step.current}/{step.total}
                  </span>
                )}
              </div>

              {step.status === "error" && step.error && (
                <p className="mt-1 text-sm text-red-600">{step.error}</p>
              )}

              {step.status === "in-progress" &&
                step.current !== undefined &&
                step.total !== undefined && (
                  <div className="mt-1">
                    <Progress
                      value={getProgressPercentage(step)}
                      className="h-1.5"
                    />
                  </div>
                )}
            </div>
          </div>
        ))}
      </div>

      {/* Summary */}
      <div className="text-muted-foreground border-t pt-2 text-center text-sm">
        {completedSteps} of {totalSteps} steps completed
      </div>
    </div>
  );
}

// Helper function to create initial steps
export function createUploadSteps(): UploadProgressStep[] {
  return [
    {
      id: "parse",
      label: "Parsing spreadsheet data",
      status: "pending",
    },
    {
      id: "validate",
      label: "Validating records",
      status: "pending",
    },
    {
      id: "match",
      label: "Matching with existing stores",
      status: "pending",
    },
    {
      id: "insert",
      label: "Inserting records into database",
      status: "pending",
    },
  ];
}

// Helper function to update a specific step
export function updateStep(
  steps: UploadProgressStep[],
  stepId: string,
  updates: Partial<UploadProgressStep>,
): UploadProgressStep[] {
  return steps.map((step) =>
    step.id === stepId ? { ...step, ...updates } : step,
  );
}
