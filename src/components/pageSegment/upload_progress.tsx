"use client";

import { Progress } from "~/components/ui/progress";
import { CheckCircle, Loader2, Circle } from "lucide-react";

export interface UploadProgressStep {
  id: string;
  label: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  current?: number;
  total?: number;
  error?: string;
}

interface UploadProgressProps {
  steps: UploadProgressStep[];
  currentStep?: string;
  overallProgress?: number;
  className?: string;
}

export function UploadProgress({
  steps,
  currentStep,
  overallProgress,
  className = ""
}: UploadProgressProps) {
  const getStepIcon = (step: UploadProgressStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'in-progress':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'error':
        return <Circle className="h-5 w-5 text-red-500" />;
      default:
        return <Circle className="h-5 w-5 text-gray-300" />;
    }
  };

  const getProgressPercentage = (step: UploadProgressStep) => {
    if (step.status === 'completed') return 100;
    if (step.status === 'pending') return 0;
    if (step.current !== undefined && step.total !== undefined && step.total > 0) {
      return Math.round((step.current / step.total) * 100);
    }
    return 0;
  };

  const currentStepData = steps.find(step => step.id === currentStep);
  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const totalSteps = steps.length;
  const calculatedOverallProgress = overallProgress ?? Math.round((completedSteps / totalSteps) * 100);
