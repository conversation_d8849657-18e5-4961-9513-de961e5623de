import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { CalendarRangePicker } from "~/components/ui/calendarrangepicker";
import { Button } from "~/components/ui/button";
import { type newStoreRow, publisherToFunction } from "~/lib/ingest/common";
import { findExistingStores, insertRecords } from "~/lib/ingest/insert";
import { type DateRange } from "react-day-picker";
import type { InferSelectModel } from "drizzle-orm";
import type { reportTypes } from "~/server/db/schema";
import type { getAllPublishers, getPublisher } from "~/server/queries";
import type * as XLSX from "xlsx";
import { 
  UploadProgress, 
  createUploadSteps, 
  updateStep,
  type UploadProgressStep 
} from "./upload_progress";

interface SheetOptionsProps {
  publishers: getAllPublishers;
  workbook: XLSX.WorkBook | null;
  worksheet: XLSX.WorkSheet | undefined;
  selectedPublisher: getPublisher;
  selectedReportType: InferSelectModel<typeof reportTypes> | undefined;
  dateRange: DateRange | undefined;
  setDateRange: (date: DateRange | undefined) => void;
  setNewStores: (stores: newStoreRow[] | null) => void;
  isPending: boolean;
  error: string | null;
  spreadsheet: File;
  setSelectedPublisher: (publisher: getPublisher) => void;
  setWorksheet: (worksheet: XLSX.WorkSheet | undefined) => void;
  setSelectedReportType: (
    reportType: InferSelectModel<typeof reportTypes> | undefined,
  ) => void;
}

export function SheetOptionsWithProgress(props: SheetOptionsProps) {
  const [uploadSteps, setUploadSteps] = useState<UploadProgressStep[]>(createUploadSteps());
  const [currentStep, setCurrentStep] = useState<string | undefined>();
  const [isUploading, setIsUploading] = useState(false);

  const updateStepState = (stepId: string, updates: Partial<UploadProgressStep>) => {
    setUploadSteps(prev => updateStep(prev, stepId, updates));
  };

  const handleParseSubmit = async () => {
    if (
      !props.workbook ||
      !props.worksheet ||
      !props.selectedReportType ||
      !props.selectedPublisher
    )
      return;

    setIsUploading(true);
    setUploadSteps(createUploadSteps()); // Reset steps
    
    try {
      // Step 1: Parse spreadsheet data
      setCurrentStep('parse');
      updateStepState('parse', { status: 'in-progress' });
      
      const cleanRecords = await publisherToFunction[
        props.selectedPublisher.name
      ]!(props.worksheet);
      
      updateStepState('parse', { 
        status: 'completed',
        current: cleanRecords.length,
        total: cleanRecords.length
      });

      // Step 2: Validate records (this step is implicit in the parsing)
      setCurrentStep('validate');
      updateStepState('validate', { status: 'in-progress' });
      
      // Simulate validation progress
      for (let i = 0; i <= cleanRecords.length; i += Math.ceil(cleanRecords.length / 10)) {
        updateStepState('validate', { 
          status: 'in-progress',
          current: Math.min(i, cleanRecords.length),
          total: cleanRecords.length
        });
        await new Promise(resolve => setTimeout(resolve, 100)); // Small delay for visual feedback
      }
      
      updateStepState('validate', { status: 'completed' });

      // Step 3: Match with existing stores
      setCurrentStep('match');
      updateStepState('match', { status: 'in-progress' });
      
      const recordsWithIds = await findExistingStores(
        props.selectedPublisher.name,
        cleanRecords,
      );
      
      updateStepState('match', { 
        status: 'completed',
        current: recordsWithIds.existing.length + recordsWithIds.new.length,
        total: cleanRecords.length
      });

      console.log("Processing complete", {
        recordsWithIds,
      });

      // Step 4: Insert records
      if (recordsWithIds.existing.length > 0) {
        setCurrentStep('insert');
        updateStepState('insert', { status: 'in-progress' });
        
        // Simulate batch insertion progress
        const batchSize = Math.ceil(recordsWithIds.existing.length / 5);
        for (let i = 0; i < recordsWithIds.existing.length; i += batchSize) {
          const batch = recordsWithIds.existing.slice(i, i + batchSize);
          await insertRecords(batch, props.dateRange);
          
          updateStepState('insert', {
            status: 'in-progress',
            current: Math.min(i + batchSize, recordsWithIds.existing.length),
            total: recordsWithIds.existing.length
          });
          
          // Small delay for visual feedback
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        updateStepState('insert', { status: 'completed' });
      } else {
        updateStepState('insert', { status: 'completed', current: 0, total: 0 });
      }

      console.log(`Uploaded ${recordsWithIds.existing.length} records`);
      console.log(recordsWithIds.new);
      props.setNewStores(recordsWithIds.new);
      
      setCurrentStep(undefined);
      
    } catch (err) {
      console.error("Error processing data", err);
      
      // Mark current step as error
      if (currentStep) {
        updateStepState(currentStep, { 
          status: 'error', 
          error: err instanceof Error ? err.message : 'Unknown error occurred'
        });
      }
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <>
      <div className="space-y-4">
        <p className="w-96 text-center text-lg">
          {props.spreadsheet.name}
          {(props.isPending || isUploading) && " (Processing...)"}
        </p>

        {props.error && <p className="text-sm text-red-500">{props.error}</p>}

        {/* Show progress during upload */}
        {isUploading && (
          <div className="w-96">
            <UploadProgress 
              steps={uploadSteps}
              currentStep={currentStep}
            />
          </div>
        )}

        <Select
          onValueChange={(value) => {
            props.setSelectedPublisher(
              props.publishers.find((p) => p.id.toString() === value),
            );
          }}
          disabled={isUploading}
        >
          <SelectTrigger className="size-96">
            <SelectValue placeholder="Specify the publisher" />
          </SelectTrigger>
          <SelectContent>
            {props.publishers.map((publisher) => (
              <SelectItem key={publisher.id} value={publisher.id.toString()}>
                {publisher.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          onValueChange={(value) => {
            props.setWorksheet(props.workbook?.Sheets[value]);
          }}
          disabled={isUploading}
        >
          <SelectTrigger className={"size-96"}>
            <SelectValue placeholder="Specify the worksheet" />
          </SelectTrigger>
          <SelectContent>
            {props.workbook?.SheetNames.map((worksheet) => (
              <SelectItem key={worksheet} value={worksheet}>
                {worksheet}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          disabled={!props.selectedPublisher || isUploading}
          onValueChange={(value) => {
            props.setSelectedReportType(
              props.selectedPublisher?.reportTypes.find(
                (p) => p.id.toString() === value,
              ),
            );
          }}
        >
          <SelectTrigger className={"size-96"}>
            <SelectValue placeholder="Type of report" />
          </SelectTrigger>
          <SelectContent>
            {props.selectedPublisher?.reportTypes.map((reportType) => (
              <SelectItem key={reportType.id} value={reportType.id.toString()}>
                {reportType.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {props.selectedReportType?.dateNeeded && (
          <CalendarRangePicker
            dateRange={props.dateRange}
            setDateRange={props.setDateRange}
            disabled={isUploading}
          />
        )}

        {props.selectedReportType &&
          ((props.selectedReportType.dateNeeded && props.dateRange?.to) ??
            !props.selectedReportType.dateNeeded) && (
            <Button
              onClick={() => void handleParseSubmit()}
              disabled={props.isPending || !props.workbook || isUploading}
            >
              {isUploading ? "Processing..." : props.isPending ? "Processing..." : "Submit"}
            </Button>
          )}
      </div>
    </>
  );
}
